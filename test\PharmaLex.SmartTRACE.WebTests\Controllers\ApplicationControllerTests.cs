﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.Extensions.Caching.Distributed;
using NSubstitute;
using NuGet.Protocol;
using PharmaLex.Caching;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Data.Persistance.Repository;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web;
using PharmaLex.SmartTRACE.Web.Controllers;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Models;
using PharmaLex.SmartTRACE.Web.Models.ViewModels;
using System.Collections.Concurrent;
using System.Security.Claims;
using System.Text;
using PharmaLex.SmartTRACE.WebTests;

namespace PharmaLex.SmartTRACE.WebTests.Controllers
{
    public class ApplicationsControllerTests
    {
        readonly ApplicationsController sut;
        readonly IAuthorizationService authService;
        readonly IApplicationExport appExport;
        readonly IDistributedCacheServiceFactory distributedCacheServiceFactory;
        readonly IMapper mapper;
        readonly SmartTRACEContext dbCtx;
        readonly HttpContext context;
        public ApplicationsControllerTests()
        {
            #region TestData and config
            var config = TestHelpers.GetConfiguration();
            var dbCtxReslover = TestHelpers.GetPlxDbContextResolver();
            dbCtx = (SmartTRACEContext)dbCtxReslover.Context;

            AddTestData(dbCtx);
            #endregion

            #region Fake Httpcontext with identity
            var httpContextAccessor = new HttpContextAccessor();
            context = new DefaultHttpContext();
            context.User = new ClaimsPrincipal(new ClaimsIdentity(new List<System.Security.Claims.Claim> {
                new System.Security.Claims.Claim("emails", "<EMAIL>"),
                new System.Security.Claims.Claim("plx:userid", "3")
            }));
            httpContextAccessor.HttpContext = context;
            var userCtx = new PlxUserContext(httpContextAccessor);
            #endregion

            #region Fake Cache
            var cacheKey = config.GetSection("Static:App").Value + "|" + config.GetSection("Static:Env").Value + "|CacheDependencyMap";
            var ds = new ConcurrentDictionary<string, List<string>>(
               new List<KeyValuePair<string, List<string>>> { new KeyValuePair<string, List<string>>("", new List<string> { "" }) }).ToJson();
            var fakeCache = Substitute.For<IDistributedCache>();
            fakeCache.Get(cacheKey).Returns(Encoding.UTF8.GetBytes(ds));
            #endregion

            #region Fake repofactory and Distributed cache serviice
            var repoFactory = new RepositoryFactory(dbCtxReslover, userCtx);
            var appRepository = new ApplicationRepository(dbCtx, userCtx);
            var cacheService = new DistributedCacheService(fakeCache, config);
            mapper = new MapperConfiguration(
            cfg =>
            {
                cfg.AddProfile<PicklistDataMappingProfile>();
                cfg.AddProfile<ActiveSubstanceMappingProfile>();
                cfg.AddProfile<ProductMappingProfile>();
                cfg.AddProfile<CountryMappingProfile>();
                cfg.AddProfile<ClientMappingProfile>();
                cfg.AddProfile<ApplicationMappingProfile>();
                cfg.AddProfile<SubmissionMappingProfile>();
                cfg.AddProfile<DocumentMappingProfile>();
                cfg.AddProfile<SubmissionResourceMappingProfile>();
            }).CreateMapper();
            distributedCacheServiceFactory = new DistributedCacheServiceFactory(repoFactory, cacheService, mapper, config);
            #endregion

            #region controller and Tempdata
            authService = Substitute.For<IAuthorizationService>();
            var appService = new ApplicationService(distributedCacheServiceFactory, mapper, authService, appRepository);
            appExport = new ApplicationExport(appService);

            sut = new ApplicationsController(distributedCacheServiceFactory, mapper, appService, appExport, authService);
            sut.ControllerContext = new ControllerContext { HttpContext = context };
            var tempData = new TempDataDictionary(context, Substitute.For<ITempDataProvider>());
            tempData.TryGetValue("UserNotification", out var userNotificationModel);
            tempData.Set("td", "td");
            sut.TempData = tempData;
            #endregion
        }

        #region Test Methods

        #region GET Methods
        [Theory]
        [InlineData(true), InlineData(false)]
        public async Task Index_Get_returns(bool isValid)
        {
            //Action
            if (!isValid)
                sut.ModelState.AddModelError("id", "error");
            var authRes = AuthorizationResult.Success();
            authService.AuthorizeAsync(Arg.Is<ClaimsPrincipal>(cp => cp.Identity != null), Arg.Any<object>(), "BusinessAdmin").Returns(Task.FromResult(authRes));
            var result = await sut.Index();

            //Assert
            var actual = Assert.IsType<ViewResult>(result);
            Assert.NotNull(actual);
            var model = Assert.IsType<ApiPagedListResult<ApplicationViewModel>>(actual.Model);
            Assert.NotNull(model);

            if (isValid)
            {
                var data = Assert.IsAssignableFrom<IList<ApplicationViewModel>>(model.Data);
                Assert.NotEmpty(data);
            }
        }

        [Theory]
        [InlineData(true), InlineData(false)]
        public async Task IndexPagedApplications_Get_returns(bool isValid)
        {
            //Action
            if (!isValid)
                sut.ModelState.AddModelError("id", "error");
            var authRes = AuthorizationResult.Success();
            authService.AuthorizeAsync(Arg.Is<ClaimsPrincipal>(cp => cp.Identity != null), Arg.Any<object>(), "BusinessAdmin").Returns(Task.FromResult(authRes));
            var result = await sut.IndexPagedApplications();

            //Assert
            var actual = Assert.IsType<JsonResult>(result);
            Assert.NotNull(actual);
            var model = Assert.IsType<ApiPagedListResult<ApplicationViewModel>>(actual.Value);
            Assert.NotNull(model);

            if (isValid)
            {
                var data = Assert.IsAssignableFrom<IList<ApplicationViewModel>>(model.Data);
                Assert.NotEmpty(data);
            }
        }

        [Theory]
        [InlineData(false, false, 23), InlineData(true, false, 23)]
        [InlineData(true, true, 23), InlineData(true, true, 24), InlineData(true, true, 25), InlineData(true, true, 26), InlineData(true, true, 27), InlineData(true, true, 28)]
        public async Task View_Get_returns(bool isValid, bool isAdmin, int id)
        {
            //Action
            if (!isValid)
                sut.ModelState.AddModelError("id", "error");
            var authRes = isAdmin ? AuthorizationResult.Success() : AuthorizationResult.Failed();
            authService.AuthorizeAsync(Arg.Is<ClaimsPrincipal>(cp => cp.Identity != null), Arg.Any<object>(), "Admin").Returns(Task.FromResult(authRes));
            var result = await sut.View(id);

            //Assert
            if (id == 28)
                Assert.IsType<ForbidResult>(result);
            else
            {
                var actual = Assert.IsType<ViewResult>(result);
                Assert.NotNull(actual);
                var model = Assert.IsType<ApplicationViewModel>(actual.Model);
                Assert.NotNull(model);
            }
        }

        [Theory]
        [InlineData(true, false), InlineData(false, true), InlineData(true, true)]
        public async Task New_Get_returns(bool isValid, bool isEdit)
        {
            //Action
            if (!isValid)
                sut.ModelState.AddModelError("id", "error");
            var authRes = AuthorizationResult.Success();
            authService.AuthorizeAsync(Arg.Is<ClaimsPrincipal>(cp => cp.Identity != null), Arg.Any<object>(), "BusinessAdmin").Returns(Task.FromResult(authRes));
            var result = await (isEdit ? sut.Edit(23) : sut.New());

            //Assert
            var actual = Assert.IsType<ViewResult>(result);
            Assert.NotNull(actual);
            var model = Assert.IsType<EditApplicationViewModel>(actual.Model);
            Assert.NotNull(model);

            if (isValid)
            {
                Assert.NotEmpty(model.Clients);
            }
        }

        [Theory]
        [InlineData(24), InlineData(25), InlineData(26), InlineData(27)]
        public async Task Edit_Get_Extra_returns(int id)
        {
            //Action
            var authRes = AuthorizationResult.Success();
            authService.AuthorizeAsync(Arg.Is<ClaimsPrincipal>(cp => cp.Identity != null), Arg.Any<object>(), "BusinessAdmin").Returns(Task.FromResult(authRes));
            var result = await sut.Edit(id);

            //Assert
            var actual = Assert.IsType<ViewResult>(result);
            Assert.NotNull(actual);
            var model = Assert.IsType<EditApplicationViewModel>(actual.Model);
            Assert.NotNull(model);
            Assert.NotEmpty(model.Clients);
        }
        #endregion

        #region POST Methods
        [Theory]
        [InlineData(true), InlineData(false)]
        public async Task New_Post_returns(bool isValid)
        {
            //Action
            var inPutmodel = new EditApplicationViewModel { Application = new ApplicationModel { ApplicationNumber = "123" } };
            if (!isValid)
                sut.ModelState.AddModelError("id", "error");
            var authRes = AuthorizationResult.Success();
            authService.AuthorizeAsync(Arg.Is<ClaimsPrincipal>(cp => cp.Identity != null), Arg.Any<object>(), "BusinessAdmin").Returns(Task.FromResult(authRes));
            var result = await sut.New(inPutmodel);

            //Assert
            if (isValid)
            {
                var actual = Assert.IsType<RedirectResult>(result);
                Assert.StartsWith("/applications/view", actual.Url);
            }
            else
            {
                var actual = Assert.IsType<ViewResult>(result);
                Assert.NotNull(actual);
                var model = Assert.IsType<EditApplicationViewModel>(actual.Model);
                Assert.NotNull(model);
            }
        }

        [Theory]
        [InlineData(true), InlineData(false)]
        public async Task Edit_Post_returns(bool isValid)
        {
            //Action
            var appService = Substitute.For<IApplicationService>();
            var uSut = new ApplicationsController(distributedCacheServiceFactory, mapper, appService, appExport, authService);
            uSut.ControllerContext = new ControllerContext { HttpContext = context };
            var tempData = new TempDataDictionary(context, Substitute.For<ITempDataProvider>());
            tempData.TryGetValue("UserNotification", out var userNotificationModel);
            tempData.Set("td", "td");
            uSut.TempData = tempData;
            var inPutmodel = new EditApplicationViewModel { Application = new ApplicationModel { Id = 23, ApplicationNumber = "123" } };
            var pickLists = GetPickLists(dbCtx, mapper);
            var getApp = () => dbCtx.Application.First();
            appService.UpdateApplication(Arg.Is<EditApplicationViewModel>(x => x.Application.Id == 23), Arg.Any<IList<PicklistDataModel>>()).Returns(Task.FromResult(getApp()));
            if (!isValid)
                uSut.ModelState.AddModelError("id", "error");
            var authRes = AuthorizationResult.Success();
            authService.AuthorizeAsync(Arg.Is<ClaimsPrincipal>(cp => cp.Identity != null), Arg.Any<object>(), "BusinessAdmin").Returns(Task.FromResult(authRes));
            var result = await uSut.Edit(23, inPutmodel);

            //Assert
            if (isValid)
            {
                var actual = Assert.IsType<RedirectResult>(result);
                Assert.StartsWith("/applications/view", actual.Url);
            }
            else
            {
                var actual = Assert.IsType<BadRequestObjectResult>(result);
                Assert.NotNull(result);
            }
        }

        [Theory]
        [InlineData(true), InlineData(false)]
        public async Task Edit_Post_ConcernedMemberStatesRequired_Persisted(bool concernedMemberStatesRequired)
        {
            //Arrange
            var appService = Substitute.For<IApplicationService>();
            var uSut = new ApplicationsController(distributedCacheServiceFactory, mapper, appService, appExport, authService);
            uSut.ControllerContext = new ControllerContext { HttpContext = context };
            var tempData = new TempDataDictionary(context, Substitute.For<ITempDataProvider>());
            tempData.TryGetValue("UserNotification", out var userNotificationModel);
            tempData.Set("td", "td");
            uSut.TempData = tempData;

            var inputModel = new EditApplicationViewModel
            {
                Application = new ApplicationModel
                {
                    Id = 23,
                    ApplicationNumber = "123",
                    ProcedureTypeId = 64 // Decentralised Procedure
                },
                ConcernedMemberStatesRequired = concernedMemberStatesRequired
            };

            var pickLists = GetPickLists(dbCtx, mapper);
            var getApp = () => dbCtx.Application.First();
            appService.UpdateApplication(Arg.Is<EditApplicationViewModel>(x => x.ConcernedMemberStatesRequired == concernedMemberStatesRequired), Arg.Any<IList<PicklistDataModel>>()).Returns(Task.FromResult(getApp()));

            var authRes = AuthorizationResult.Success();
            authService.AuthorizeAsync(Arg.Is<ClaimsPrincipal>(cp => cp.Identity != null), Arg.Any<object>(), "BusinessAdmin").Returns(Task.FromResult(authRes));

            //Act
            var result = await uSut.Edit(23, inputModel);

            //Assert
            var actual = Assert.IsType<RedirectResult>(result);
            Assert.StartsWith("/applications/view", actual.Url);

            // Verify that the service was called with the correct ConcernedMemberStatesRequired value
            await appService.Received(1).UpdateApplication(Arg.Is<EditApplicationViewModel>(x => x.ConcernedMemberStatesRequired == concernedMemberStatesRequired), Arg.Any<IList<PicklistDataModel>>());
        }

        [Theory]
        [InlineData(true), InlineData(false)]
        public async Task Export_Post_returns(bool isValid)
        {
            //Action
            var inPutmodel = new ApplicationFilterModel();
            if (!isValid)
                sut.ModelState.AddModelError("id", "error");
            var authRes = AuthorizationResult.Success();
            authService.AuthorizeAsync(Arg.Is<ClaimsPrincipal>(cp => cp.Identity != null), Arg.Any<object>(), "BusinessAdmin").Returns(Task.FromResult(authRes));
            var result = await sut.Export(inPutmodel);

            //Assert
            if (isValid)
            {
                var actual = Assert.IsAssignableFrom<FileResult>(result);
                Assert.StartsWith("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", actual.ContentType);
            }
            else
            {
                var actual = Assert.IsType<JsonResult>(result);
                Assert.NotNull(actual);
            }
        }

        [Theory]
        [InlineData(true), InlineData(false)]
        public async Task ChangeState_Post_returns(bool isValid)
        {
            //Action
            var inPutmodel = new ApplicationFilterModel();
            if (!isValid)
                sut.ModelState.AddModelError("id", "error");
            var authRes = AuthorizationResult.Success();
            authService.AuthorizeAsync(Arg.Is<ClaimsPrincipal>(cp => cp.Identity != null), Arg.Any<object>(), "BusinessAdmin").Returns(Task.FromResult(authRes));
            var result = await sut.ChangeState(23, "2");

            //Assert
            if (isValid)
            {
                var actual = Assert.IsType<OkObjectResult>(result);
                Assert.Equal("2", actual.Value?.ToString());
            }
            else
            {
                var actual = Assert.IsType<BadRequestObjectResult>(result);
                Assert.NotNull(actual);
            }
        }
        #endregion

        #endregion

        #region Static Helpers
        private static void AddTestData(SmartTRACEContext dbCtx)
        {

            dbCtx.PicklistData.AddRange(new List<PicklistData> {
                new PicklistData { Name = "Human Use", PicklistTypeId = 1, CreatedBy = "test", LastUpdatedBy="test" },
                new PicklistData { Name = "Clinical", PicklistTypeId = 5, CreatedBy = "test", LastUpdatedBy="test" },
                new PicklistData { Name = "National Procedure", PicklistTypeId = 5, CreatedBy = "test", LastUpdatedBy="test" },
                new PicklistData { Name = "GCC National Procedure", PicklistTypeId = 5, CreatedBy = "test", LastUpdatedBy="test" },
                new PicklistData { Name = "Decentralised Procedure", PicklistTypeId = 5, CreatedBy = "test", LastUpdatedBy="test" },
                new PicklistData { Name = "Mutual Recognition Procedure", PicklistTypeId = 5, CreatedBy = "test", LastUpdatedBy="test" },
                new PicklistData { Name = "ASMF Worksharing Procedure", PicklistTypeId = 5, CreatedBy = "test", LastUpdatedBy="test" },
                new PicklistData { Name = "Normal", PicklistTypeId = 4, CreatedBy = "test", LastUpdatedBy="test" }
            });

            dbCtx.Application.Add(new Application { Id = 23, ApplicationNumber = "AP-20210113-1 APPLICATION TEST", MedicinalProductDomainId = 1, MedicinalProductTypeId = 5, ApplicationTypeId = 3, ProcedureTypeId = 2, Comments = null, LifecycleStateId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            dbCtx.Application.Add(new Application { Id = 24, ApplicationNumber = "AP-RSUB/REG LEAD", MedicinalProductDomainId = 1, MedicinalProductTypeId = 5, ApplicationTypeId = 3, ProcedureTypeId = 3, Comments = null, LifecycleStateId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            dbCtx.Application.Add(new Application { Id = 25, ApplicationNumber = "AP-RSUB/REG LEAD", MedicinalProductDomainId = 1, MedicinalProductTypeId = 5, ApplicationTypeId = 3, ProcedureTypeId = 4, Comments = null, LifecycleStateId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            dbCtx.Application.Add(new Application { Id = 26, ApplicationNumber = "AP-RSUB/REG LEAD", MedicinalProductDomainId = 1, MedicinalProductTypeId = 5, ApplicationTypeId = 3, ProcedureTypeId = 5, Comments = null, LifecycleStateId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            dbCtx.Application.Add(new Application { Id = 27, ApplicationNumber = "AP-RSUB/REG LEAD", MedicinalProductDomainId = 1, MedicinalProductTypeId = 5, ApplicationTypeId = 3, ProcedureTypeId = 6, Comments = null, LifecycleStateId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            dbCtx.Application.Add(new Application { Id = 28, ApplicationNumber = "AP-RSUB/REG LEAD", MedicinalProductDomainId = 1, MedicinalProductTypeId = 5, ApplicationTypeId = 3, ProcedureTypeId = 7, Comments = null, LifecycleStateId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });

            dbCtx.Product.Add(new Product { Id = 5, Name = "Fusce velit dolor 100 mg", DosageFormId = 3, Strength = "100 mg", ClientId = 1, LifecycleStateId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            dbCtx.Product.Add(new Product { Id = 6, Name = "Fusce velit dolor 150 mg", DosageFormId = 3, Strength = "150 mg", ClientId = 2, LifecycleStateId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            dbCtx.Product.Add(new Product { Id = 7, Name = "Fusce velit dolor 200 mg", DosageFormId = 3, Strength = "200 mg", ClientId = 1, LifecycleStateId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });

            dbCtx.Region.Add(new Region { Id = 6, Name = "European Union", Abbreviation = "EU", CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });

            dbCtx.Country.Add(new Country { Id = 1, Name = "Austria", RegionId = 6, TwoLetterCode = "AT", CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            dbCtx.Country.Add(new Country { Id = 2, Name = "Belgium", RegionId = 6, TwoLetterCode = "BE", CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });

            dbCtx.ApplicationProduct.Add(new ApplicationProduct { Id = 29, ApplicationId = 23, ProductId = 5, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            dbCtx.ApplicationProduct.Add(new ApplicationProduct { Id = 28, ApplicationId = 24, ProductId = 5, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            dbCtx.ApplicationProduct.Add(new ApplicationProduct { Id = 32, ApplicationId = 25, ProductId = 5, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            dbCtx.ApplicationProduct.Add(new ApplicationProduct { Id = 31, ApplicationId = 26, ProductId = 5, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            dbCtx.ApplicationProduct.Add(new ApplicationProduct { Id = 30, ApplicationId = 27, ProductId = 5, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            dbCtx.ApplicationProduct.Add(new ApplicationProduct { Id = 33, ApplicationId = 28, ProductId = 6, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });


            dbCtx.ApplicationCountry.Add(new ApplicationCountry { Id = 95, ApplicationId = 23, CountryId = 1, AuthorityRoleId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });


            dbCtx.ApplicationCountry.Add(new ApplicationCountry { Id = 102, ApplicationId = 23, CountryId = 1, AuthorityRoleId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            dbCtx.ApplicationCountry.Add(new ApplicationCountry { Id = 96, ApplicationId = 24, CountryId = 2, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            dbCtx.ApplicationCountry.Add(new ApplicationCountry { Id = 97, ApplicationId = 25, CountryId = 2, AuthorityRoleId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            dbCtx.ApplicationCountry.Add(new ApplicationCountry { Id = 98, ApplicationId = 26, CountryId = 2, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });


            dbCtx.Client.Add(new Client { Id = 1, Name = "Astra", ContractOwnerId = 152, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            dbCtx.Client.Add(new Client { Id = 2, Name = "Astra", ContractOwnerId = 152, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });

            dbCtx.User.Add(new User { Id = 3, Email = "<EMAIL>", GivenName = "Tanya", FamilyName = "Lisseva", CreatedBy = "update script", LastUpdatedBy = "<EMAIL>", UserTypeId = 1 });
            dbCtx.User.Add(new User { Id = 4, Email = "<EMAIL>", GivenName = "Tanya", FamilyName = "Lisseva", CreatedBy = "update script", LastUpdatedBy = "<EMAIL>", UserTypeId = 1 });

            dbCtx.UserClient.Add(new UserClient { ClientId = 1, UserId = 3, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            dbCtx.UserClient.Add(new UserClient { ClientId = 2, UserId = 4, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });

            dbCtx.Claim.Add(new PharmaLex.SmartTRACE.Entities.Claim { Id = 5, Name = "Reader", ClaimType = "application", CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });

            dbCtx.Submission.Add(new Submission { ApplicationId = 23, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            dbCtx.SubmissionCountry.Add(new SubmissionCountry { SubmissionId = 1, CountryId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            dbCtx.SubmissionResource.Add(new SubmissionResource { SubmissionId = 1, RegulatoryLead = "<EMAIL>", CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            dbCtx.SaveChanges();

        }

        private static List<PicklistDataModel> GetPickLists(SmartTRACEContext dbctx, IMapper mapper)
        {
            var data = dbctx.PicklistData.Where(x => x.PicklistTypeId == 5).Select(x => mapper.Map<PicklistDataModel>(x)).ToList();
            return data;
        }
        #endregion
    }
}
