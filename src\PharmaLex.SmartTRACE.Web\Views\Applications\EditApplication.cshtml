﻿@model EditApplicationViewModel
@using Newtonsoft.Json
@using Newtonsoft.Json.Serialization
@using PharmaLex.Caching.Data
@using PharmaLex.SmartTRACE.Entities
@using PharmaLex.SmartTRACE.Entities.Enums
@inject IDistributedCacheServiceFactory cache
@inject AutoMapper.IMapper mapper

@{
    var procedureTypeSelectList = Model.Picklists.Where(x => x.PicklistTypeId == (int)PicklistType.ProcedureType).OrderBy(x => x.Name).Select(x => new SelectListItem
            {
                Text = $"{x.Name}",
                Value = x.Id.ToString()
            });
    var medicinalProductDomainSelectList = Model.Picklists.Where(x => x.PicklistTypeId == (int)PicklistType.MedicinalProductDomain).OrderBy(x => x.Name).Select(x => new SelectListItem
            {
                Text = $"{x.Name}",
                Value = x.Id.ToString()
            });
    var medicinalProductTypeSelectList = Model.Picklists.Where(x => x.PicklistTypeId == (int)PicklistType.MedicinalProductType).OrderBy(x => x.Name).Select(x => new SelectListItem
            {
                Text = $"{x.Name}",
                Value = x.Id.ToString()
            });
    var productSelectList = Model.Products.OrderBy(x => x.Name).Select(x => new SelectListItem
            {
                Text = $"{x.Name}",
                Value = x.Id.ToString(),
            });
    var clientSelectList = Model.Clients.OrderBy(x => x.Name).Select(x => new SelectListItem
            {
                Text = $"{x.Name}",
                Value = x.Id.ToString()
            });

    var dossierFormats = await this.cache.CreateMappedEntity<PicklistData, PicklistDataModel>()
                                           .WhereAsync(x => x.PicklistTypeId == (int)PicklistType.DossierFormat);
    var submissionTypes = await this.cache.CreateMappedEntity<PicklistData, PicklistDataModel>()
                                          .WhereAsync(x => x.PicklistTypeId == (int)PicklistType.SubmissionType);
    var submissionUnits = await this.cache.CreateMappedEntity<PicklistData, PicklistDataModel>()
                                         .WhereAsync(x => x.PicklistTypeId == (int)PicklistType.SubmissionUnit);
    var submissionModes = await this.cache.CreateMappedEntity<PicklistData, PicklistDataModel>()
                                         .WhereAsync(x => x.PicklistTypeId == (int)PicklistType.SubmissionMode);
    var procedureTypes = await this.cache.CreateMappedEntity<PicklistData, PicklistDataModel>()
                                         .WhereAsync(x => x.PicklistTypeId == (int)PicklistType.ProcedureType);
    var applicationTypes = await this.cache.CreateMappedEntity<PicklistData, PicklistDataModel>()
                                        .WhereAsync(x => x.PicklistTypeId == (int)PicklistType.ApplicationType);
    var lifecycleStates = mapper.Map<IEnumerable<SubmissionLifecycleStateList>>(Enum.GetValues(typeof(SubmissionLifeCycleState)));
    var actSubstanceProducts = await this.cache.CreateEntity<ActiveSubstanceProduct>().AllAsync();
    var picklistType = mapper.Map<IEnumerable<PicklistTypeList>>(Enum.GetValues(typeof(PicklistType)));
    var applicationsLifecycleStates = mapper.Map<IEnumerable<ApplicationLifecycleStateList>>(Enum.GetValues(typeof(CommonLifecycleState)));
}

<div id="application" class="manage-container" v-cloak>
    <header class="manage-header">
        <h3>@Model.Application.ApplicationNumber Application</h3>
        <div class="common-state-container" :stateId="application.lifecycleStateId" v-if="application.lifecycleState">
            <span class="common-state">{{ application.lifecycleState }}</span>
        </div>

        <a href="/applications" class="button secondary icon-button-list">View All Applications</a>
        <div v-on:mouseover="showStatesDropdown()" v-on:mouseleave="hideStatesDropdown()" v-show="showMoveToButton()">
            <button type="button" class="button icon-button-next">Move To</button>
            <div v-show="showStates" class="common-state-main-button" v-on:mouseover="showStatesDropdown()" v-on:mouseleave="hideStatesDropdown()">
                <a name="moveToState" class="common-state-button" v-on:click="moveTo(withdrawnStateId)">{{ withdrawnState }}</a>
                <a name="moveToState" class="common-state-button" v-on:click="moveTo(obsoleteStateId)" v-show="!checkExistingSubmissions()">{{ obsoleteState }}</a>
            </div>
        </div>
        @if (Model.Application.Id != 0)
        {
            <a href="/applications/view/@Model.Application.Id" class="button icon-button-list">View</a>
            <a href="/submission-request/new/@Model.Application.Id" class="button icon-button-add" v-if="!appInObsoleteOrWithdrawnState()">Create Submission Record</a>
        }
    </header>
    <form id="application-form" method="post">
        @Html.AntiForgeryToken()
        <div class="form-col form-col-half">
            <h5>Application Details</h5>
            <label for="Application.ApplicationNumber">Number*</label>
            <input asp-for="Application.ApplicationNumber" type="text" required />
            <div id="validationMessage" style="color:red;padding:10px"></div>
            <label for="Application.MedicinalProductDomainId">Medicinal Product Domain*</label>
            <div class="select-wrapper">
                <select asp-for="Application.MedicinalProductDomainId" asp-items="medicinalProductDomainSelectList" required>
                    <option value="">Select medicinal product domain</option>
                </select>
            </div>
            <label for="Application.MedicinalProductTypeId">Medicinal Product Type*</label>
            <div class="select-wrapper">
                <select asp-for="Application.MedicinalProductTypeId" asp-items="medicinalProductTypeSelectList" required>
                    <option value="">Select medicinal product type</option>
                </select>
            </div>
            <label for="Application.ClientId">Client*</label>
            <div class="select-wrapper">
                <select asp-for="Application.ClientId" asp-items="clientSelectList" v-on:change="selectedClient($event)" :aria-disabled="application.submissions.length > 0" required>
                    <option value="">Select client</option>
                </select>
            </div>
            <label for="Application.ProductsIds" v-if="clientSelected">Product*</label>
            <div :class="['multi-select-wrapper', {'hidden': !clientSelected }]">
                <span v-if="missingProducts">No products available for this client.</span>
                <select asp-for="Application.ProductsIds" v-model="selectedProductIds" v-on:change="selectedProduct($event)" required>
                    <option v-for="(prod, index) in products" :key="index" v-bind:value="prod.id" :selected="selectedProd(prod)" multiple>{{ prod.name }}, {{prod.strength}}, {{ prod.dosageForm }}</option>
                </select>
            </div>
            <label for="activeSubstances" v-if="productSelected">Active Substances</label>
            <div :class="['multi-select-wrapper', {'hidden': !productSelected }]">
                <span v-if="activeSubstances.size === 0">No active substances available for these products.</span>
                <select multiple>
                    <option v-for="(ac, index) in activeSubstances" :key="index" v-bind:value="ac.id" disabled>{{ ac.name }}</option>
                </select>
            </div>
        </div>
        <div class="form-col form-col-half">
            <h5><span>-</span></h5>
            <label for="Application.ProcedureTypeId">Procedure type*</label>
            <div class="select-wrapper">
                <select asp-for="Application.ProcedureTypeId" asp-items="procedureTypeSelectList" v-on:change="selectedProcedure($event)" required>
                    <option value="">Select procedure type</option>
                </select>
            </div>
            <label for="Application.CountryId" v-if="showMemberStateLabel()">Reference Member State*</label>
            <label for="Application.CountryId" v-if="showCountryLabel()">Country*</label>
            <div :class="['select-wrapper', {'hidden': !showSingleSelect()}]">
                <select asp-for="Application.CountryId" v-model="selectedCountryId" v-on:change="selectedReferenceState($event)"
                        :required="showSingleSelect()">
                    <option value="">Select country</option>
                    <option v-for="(c, index) in referenceCountries" :key="index" v-bind:value="c.id" :selected="selectedCountry(c)">{{ c.name }}</option>
                </select>
            </div>
            <label for="Application.CountriesIds" v-if="showCountriesLabel()">Countries*</label>

            <label for="fieldActive" v-if="showMemberStateLabel()">Concerned Member States*</label>
            <label v-if="showMemberStateLabel()" class="switch-container">
                Applicable
                <input id="fieldActive" type="checkbox" class="switch" v-model="concernedMemberStatesRequired" :value="concernedMemberStatesRequired" />
                <label for="fieldActive" class="switch"></label>
                Not Applicable
            </label>

            <div :class="['multi-select-wrapper', {'hidden': !showMultiSelect()}]">
                <select asp-for="Application.CountriesIds" v-model="selectedCountriesIds" :aria-disabled="concernedMemberStatesRequired" v-on:change="selectedConcernedState($event)"
                        :required="showMultiSelect()" multiple>
                    <option v-for="(c, index) in concernedCountries" :key="index" v-bind:value="c.id" :selected="selectedCountries(c)">{{ c.name }}</option>
                </select>
            </div>
            <label for="Application.ApplicationTypeId" v-if="selectedProcedureTypeId">Application type*</label>
            <div :class="['select-wrapper', {'hidden': !selectedProcedureTypeId}]">
                <select asp-for="Application.ApplicationTypeId" v-model="selectedApplicationTypeId" :aria-disabled="checkSelectedCountry()" required>
                    <option value="">Select application type</option>
                    <option v-for="(a, index) in applicationTypes" :key="index" v-bind:value="a.id" :selected="selectedApplicationType(a)">{{ a.name }}</option>
                </select>
            </div>
            <label for="Application.Comments" asp-for="Application.Comments">Comments</label>
            <textarea asp-for="Application.Comments" v-model="comments" maxlength="250" v-on:keyup="validateCharacters()"></textarea>
            <span class="field-validation-error">{{ maxLengthCommentsMessage }}</span>
        </div>
        <div class="buttons">
            <a class="button secondary icon-button-cancel" href="/applications">Cancel</a><button class="icon-button-save">Save</button>
        </div>
        <input type="hidden" asp-for="Application.Id" />
        <input type="hidden" asp-for="Application.ClientId" />
        <input type="hidden" asp-for="Application.LifecycleStateId" />
        <input type="hidden" asp-for="ConcernedMemberStatesRequired" id="hiddenConcernedMemberStatesRequired" />

        <yes-no-dialog :config="moveToConfig" v-on:button-pressed="onMoveTo"></yes-no-dialog>
    </form>

    @if (Model.Application.Id != 0)
    {
        <br />
        <h3>Submissions</h3>
        <filtered-table :items="submissions" :columns="columns" :filters="filters" :link="link"></filtered-table>
    }
</div>

@section Scripts {
    <script src="@Cdn.GetUrl("lib/jquery/dist/jquery.min.js")"></script>
    <script type="text/javascript">
        const token = document.getElementsByName("__RequestVerificationToken")[0]?.value;
        var pageConfig = {
            appElement: '#application',
            data() {
                return {
                    link: '/submissions/view/',
                    submissions: @Html.Raw(Model.Application.Submissions.ToJson()),
                    columns: {
                        idKey: 'id',
                        styleKey: 'lifecycleState',
                        config: [
                            {
                                dataKey: 'sequenceNumber',
                                sortKey: 'sequenceNumber',
                                header: 'Sequence Number',
                                type: 'text'
                            },
                            {
                                dataKey: 'dossierFormat',
                                sortKey: 'dossierFormat',
                                header: 'Dossier Format',
                                type: 'text'
                            },
                            {
                                dataKey: 'submissionType',
                                sortKey: 'submissionType',
                                header: 'Submission Type',
                                type: 'text'
                            },
                            {
                                dataKey: 'submissionUnit',
                                sortKey: 'submissionUnit',
                                header: 'Submission Unit',
                                type: 'text'
                            },
                            {
                                dataKey: 'submissionMode',
                                sortKey: 'submissionMode',
                                header: 'Submission Mode',
                                type: 'text'
                            },
                            {
                                dataKey: 'description',
                                sortKey: 'description',
                                header: 'Submission Description',
                                type: 'text'
                            },
                            {
                                dataKey: 'lifecycleState',
                                sortKey: 'lifecycleState',
                                header: 'Lifecycle State',
                                type: 'text'
                            },
                            {
                                dataKey: 'displayRegulatoryLead',
                                sortKey: 'displayRegulatoryLead',
                                header: 'Regulatory Lead',
                                type: 'text'
                            },
                            {
                                dataKey: 'displayPublishingLead',
                                sortKey: 'displayPublishingLead',
                                header: 'Publishing Lead',
                                type: 'text'
                            }
                        ]
                    },
                    filters: [
                        {
                            key: 'sequenceNumber',
                            options: [],
                            type: 'search',
                            header: 'Search Sequence Number',
                            fn: v => p => (p.sequenceNumber || '').toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'dossierFormat',
                            options: @Html.Raw(dossierFormats.ToJson()),
                            filterCollection: 'dossierFormat',
                            display: 'id',
                            type: 'select',
                            header: 'Filter By Dossier Format',
                            fn: v => p => p.dossierFormat === v,
                            convert: v => v
                        },
                        {
                            key: 'submissionType',
                            options: [],
                            type: 'search',
                            header: 'Search Submission Type',
                            fn: v => p => (p.submissionType || '').toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'submissionUnit',
                            options: @Html.Raw(submissionUnits.ToJson()),
                            filterCollection: 'submissionUnit',
                            display: 'id',
                            type: 'select',
                            header: 'Filter By Unit',
                            fn: v => p => p.submissionUnit === v,
                            convert: v => v
                        },
                        {
                            key: 'submissionMode',
                            options: @Html.Raw(submissionModes.ToJson()),
                            filterCollection: 'submissionMode',
                            display: 'id',
                            type: 'select',
                            header: 'Filter By Mode',
                            fn: v => p => p.submissionMode === v,
                            convert: v => v
                        },
                        {
                            key: 'lifecycleState',
                            options: @Html.Raw(lifecycleStates.ToJson()),
                            filterCollection: 'lifecycleState',
                            display: 'id',
                            type: 'select',
                            header: 'Filter By State',
                            fn: v => p => p.lifecycleState === v,
                            convert: v => v
                        },
                        {
                            key: 'description',
                            options: [],
                            type: 'search',
                            header: 'Search Description',
                            fn: v => p => (p.description || '').toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'displayRegulatoryLead',
                            options: [],
                            type: 'search',
                            header: 'Search Regulatory Lead',
                            fn: v => p => (p.displayRegulatoryLead || '').toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'displayPublishingLead',
                            options: [],
                            type: 'search',
                            header: 'Search Publishing Lead',
                            fn: v => p => (p.displayPublishingLead || '').toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        }
                    ],
                    moveToConfig: {
                        message: null,
                        noButton: 'Cancel',
                        yesButton: 'Continue',
                        elementName: 'moveToState',
                        buttonClass: 'icon-button-next'
                    },
                    application: @Html.Raw(JsonConvert.SerializeObject(Model.Application, new JsonSerializerSettings { ContractResolver = new CamelCasePropertyNamesContractResolver(), StringEscapeHandling = StringEscapeHandling.EscapeHtml })),
                    allClients: @Html.Raw(Model.Clients.ToJson()),
                    allProducts: @Html.Raw(Model.Products.ToJson()),
                    allCountries: @Html.Raw(Model.AllCountries.ToJson()),
                    allActiveSubstances: @Html.Raw(Model.ActiveSubstances.ToJson()),
                    selectedClientId: @Model.Application.ClientId,
                    selectedProductIds: @Html.Raw(Json.Serialize(Model.Application.ProductsIds)),
                    selectedCountriesIds: @Html.Raw(Json.Serialize(Model.Application.CountriesIds)),
                    selectedCountryId: null,
                    actSubstanceProducts: @Html.Raw(Json.Serialize(actSubstanceProducts)),
                    procedureTypes: @Html.Raw(Json.Serialize(procedureTypes)),
                    products: [],
                    activeSubstances: [],
                    referenceCountries: [],
                    concernedCountries: [],
                    selectedProcedureTypeId: @Model.Application.ProcedureTypeId,
                    clientSelected: false,
                    productSelected: false,
                    missingProducts: true,
                    concernedMemberStatesRequired: @Model.ConcernedMemberStatesRequired.ToString().ToLower(),
                    applicationTypes: @Html.Raw(Json.Serialize(applicationTypes)),
                    selectedApplicationTypeId: @Model.Application.ApplicationTypeId,
                    picklistTypes: @Html.Raw(Json.Serialize(picklistType)),
                    allPicklists: @Html.Raw(Json.Serialize(Model.Picklists)),
                    applicationId: @Model.Application.Id,
                    referenceStateId: 0,
                    concernedStateIds: [],
                    countryLabelProcedures: ['National Procedure', 'GCC National Procedure'],
                    countriesLabelProcedures: ['Centralised Procedure', 'GCC Community Procedure'],
                    memberStateLabelProcedures: ['Decentralised Procedure', 'Mutual Recognition Procedure', 'ASMF Worksharing Procedure'],
                    singleSelectProcedures: [],
                    multiSelectProcedures: [],
                    comments: @Html.Raw(Json.Serialize(Model.Application.Comments)),
                    maxLengthCommentsMessage: '',
                    obsoleteStateId: @Html.Raw((int)CommonLifecycleState.Obsolete),
                    withdrawnStateId: @Html.Raw((int)CommonLifecycleState.Withdrawn),
                    activeStateId: @Html.Raw((int)CommonLifecycleState.Active),
                    obsoleteState: '@Html.Raw(CommonLifecycleState.Obsolete.GetDescription())',
                    withdrawnState: '@Html.Raw(CommonLifecycleState.Withdrawn.GetDescription())',
                    showStates: false,
                    selectedStateId: null,
                    appLifecycleStates: @Html.Raw(Json.Serialize(applicationsLifecycleStates)),
                }
            },
            methods: {
                validateCharacters() {
                    if (this.comments.length > 250) {
                        this.maxLengthCommentsMessage = 'Maximum 250 characters allowed';
                    } else {
                        this.maxLengthCommentsMessage = '';
                    }
                },
                handleSubmit(event) {
                    var inputValue = $('#Application_ApplicationNumber').val();
                    var unmatchedChars = findUnmatchedCharacters(inputValue);
                    if (unmatchedChars._value.length > 0) {
                        event.preventDefault();
                        $('#validationMessage').text("Name contains invalid characters: " + unmatchedChars._value.join(' '));
                        $('#validationMessage').css('color', 'red');
                        return false;
                    }
                    $('#validationMessage').text('');
                    return true;
                },
                handleChange(event) {
                    $('#Application_ApplicationNumber').change(function () {
                        $('#validationMessage').text('');
                    })
                },
                checkSelectedCountry() {
                    return this.selectedCountryId === '' && this.selectedCountriesIds.length === 0;
                },
                selectedProduct(event) {
                    this.activeSubstances = new Set();
                    for (index = 0; index < event.target.options.length; index++) {
                        let option = event.target.options[index];
                        if (option.selected) {
                            this.productSelected = true;
                            var currentActSubstances = this.actSubstanceProducts.filter(x => x.productId == option.value).map(x => x.activeSubstanceId);
                            this.activeSubstances = new Set([...this.activeSubstances, ...this.allActiveSubstances.filter(x => currentActSubstances.includes(x.id))]);
                        }
                    }
                },
                selectedApplicationType(type) {
                    this.selectedApplicationTypeId === type.id;
                },
                selectedReferenceState(event) {
                    this.referenceStateId = parseInt(event.target.value);
                    this.concernedCountries = this.referenceCountries.filter(x => x.id != this.referenceStateId);
                    this.mergeAndUpdateApplicationType();
                },
                selectedConcernedState(event) {
                    this.concernedStateIds = Array.prototype.slice.call(event.target.options).filter(x => x.selected).map(x => parseInt(x.value));
                    this.mergeAndUpdateApplicationType();
                },
                mergeAndUpdateApplicationType() {
                    let selectedCountries = this.allCountries.filter(x => x.id === this.referenceStateId);
                    selectedCountries = new Set([...selectedCountries, ...this.allCountries.filter(x => this.concernedStateIds.includes(x.id))]);
                    this.updateApplicationType(Array.from(selectedCountries));
                },
                selectedProd(prod) {
                    return this.selectedProductIds.includes(prod.id);
                },
                selectedCountries(c) {
                    return this.selectedCountriesIds.includes(c.id);
                },
                selectedCountry(c) {
                    return this.selectedCountryId === c.id;
                },
                selectedClient(event) {
                    this.products = [];
                    this.selectedProductIds = [];
                    this.activeSubstances = [];
                    this.productSelected = false;
                    let client = this.allClients.find(x => x.id == event.target.value);
                    if (client) {
                        this.products = this.allProducts.filter(x => x.clientId == client.id);
                        if (this.products.length === 0) {
                            this.clientSelected = true;
                            this.missingProducts = true;
                        } else {
                            this.clientSelected = true;
                            this.missingProducts = false;
                        }
                    } else {
                        this.clientSelected = false;
                    }
                },
                selectedProcedure(event) {
                    this.selectedCountryId = "";
                    this.selectedCountriesIds = [];
                    this.selectedApplicationTypeId = "";
                    this.referenceStateId = 0;
                    this.concernedStateIds = [];
                    this.selectedProcedureTypeId = parseInt(event.target.value);
                    this.getCountriesByProcedureType();
                },
                showSingleSelect() {
                    return this.singleSelectProcedures.includes(this.getSelectedProcedureName());
                },
                showMultiSelect() {
                    if (this.getSelectedProcedureName()) {
                        if (this.multiSelectProcedures.includes(this.getSelectedProcedureName()))
                            return true;

                        if (!this.showSingleSelect())
                            return true;

                        return false;
                    }

                    return this.multiSelectProcedures.includes(this.getSelectedProcedureName());
                },
                showCountryLabel() {
                    return this.countryLabelProcedures.includes(this.getSelectedProcedureName());
                },
                showMemberStateLabel() {
                    return this.memberStateLabelProcedures.includes(this.getSelectedProcedureName());
                },
                showCountriesLabel() {
                    if (this.getSelectedProcedureName()) {
                        if (this.countriesLabelProcedures.includes(this.getSelectedProcedureName()))
                            return true;

                        if (!this.showCountryLabel() && !this.showMemberStateLabel())
                            return true;

                        return false;
                    }

                    return this.countriesLabelProcedures.includes(this.getSelectedProcedureName());
                },
                getSelectedProcedureName() {
                    var selectedProcedure = this.procedureTypes.find(x => x.id === this.selectedProcedureTypeId)
                    if (selectedProcedure) {
                        return selectedProcedure.name;
                    }
                },
                updateApplicationType(countries) {
                    fetch(`/data/update-picklist`, this.getFetchOptions('POST', JSON.stringify(countries)))
                        .then(r => r.json())
                        .then(result => {
                            this.allPicklists = result;
                            this.applicationTypes = this.getPicklistValuesByType('Application Type');
                        });
                },
                getCountriesByProcedureType: function () {
                    fetch(`/data/countries/${this.selectedProcedureTypeId}`, this.getFetchOptions('GET'))
                        .then(r => r.json())
                        .then(result => {
                            if (result.length === 0) {
                                this.referenceCountries = this.allCountries;
                                this.concernedCountries = this.allCountries.filter(x => x.id != this.selectedCountryId);
                            } else {
                                this.referenceCountries = result;
                                this.concernedCountries = this.referenceCountries.filter(x => x.id != this.selectedCountryId);
                            }
                        });
                },
                getPicklistValuesByType(type) {
                    var picklistType = this.picklistTypes.find(t => t.name === type);
                    return this.allPicklists.filter(x => x.picklistTypeId === picklistType.id);
                },
                moveTo(stateId) {
                    this.selectedStateId = stateId;
                    let state = this.selectedStateId === this.withdrawnStateId ? this.appLifecycleStates.find(x => x.id === this.withdrawnStateId)?.name : this.appLifecycleStates.find(x => x.id === this.obsoleteStateId)?.name;
                    this.moveToConfig.message = 'Do you want to move to ' + state + '?';
                },
                showStatesDropdown() {
                    this.showStates = true;
                },
                hideStatesDropdown() {
                    this.showStates = false;
                },
                onMoveTo(yesButtonPressed) {
                    if (yesButtonPressed) {
                        fetch(`/applications/state/${this.application.id}`, this.getFetchOptions('POST', JSON.stringify(this.selectedStateId.toString())))
                            .then(r => r.json())
                            .then(stateId => {
                                let currentLifecycleState = this.appLifecycleStates.find(x => x.id === stateId);
                                if (currentLifecycleState) {
                                    this.application.lifecycleState = currentLifecycleState.name;
                                    this.application.lifecycleStateId = currentLifecycleState.id;
                                }

                                if (stateId === this.obsoleteStateId) {
                                    document.location.href = '/applications/view/' + this.application.id;
                                }
                            });
                    }

                },
                checkExistingSubmissions() {
                    return this.submissions.length > 0;
                },
                appInObsoleteOrWithdrawnState() {
                    return this.application.lifecycleStateId === this.obsoleteStateId || this.application.lifecycleStateId === this.withdrawnStateId;
                },
                showMoveToButton() {
                    return this.application.id !== 0 && this.application.lifecycleStateId === this.activeStateId;
                },
                getFetchOptions: function (method, body) {
                    return {
                        method: method,
                        credentials: 'same-origin',
                        headers: {
                            "Content-Type": "application/json",
                            'RequestVerificationToken': token
                        },
                        body: body
                    };
                },
            },
            mounted() {
                document.getElementById('application-form').addEventListener('submit', this.handleSubmit);
                document.getElementById('Application_ApplicationNumber').addEventListener('change', this.handleChange);
                if (this.comments && this.comments.length > 250) {
                    this.maxLengthCommentsMessage = 'Maximum 250 characters allowed';
                }
                if (this.selectedCountryId && this.application.countriesIds.length > 0) {
                    let selectedCountries = this.allCountries.filter(x => this.application.countriesIds.includes(x.id));
                    selectedCountries = new Set([...selectedCountries, ...this.allCountries.filter(x => x.id == this.selectedCountryId)]);
                    this.updateApplicationType(Array.from(selectedCountries));
                }
                else if (this.selectedCountryId) {
                    this.referenceStateId = parseInt(this.selectedCountryId);
                    this.concernedCountries = this.referenceCountries.filter(x => x.id != this.referenceStateId);
                    this.mergeAndUpdateApplicationType();
                } else if (this.application.countriesIds.length > 0) {
                    let selectedCountries = this.allCountries.filter(x => this.application.countriesIds.includes(x.id));
                    this.updateApplicationType(Array.from(selectedCountries));
                }
            },
            created() {
                if (this.selectedProcedureTypeId) {
                    this.getCountriesByProcedureType();
                }
                this.products = this.allProducts.filter(x => x.clientId == this.selectedClientId);
                var currentActSubstances = this.actSubstanceProducts.filter(x => this.selectedProductIds.includes(x.productId)).map(x => x.activeSubstanceId);
                this.activeSubstances = new Set(this.allActiveSubstances.filter(x => currentActSubstances.includes(x.id)));
                if (this.selectedClientId != 0) {
                    this.clientSelected = true;
                    this.missingProducts = false;
                    this.productSelected = true;
                }

                if (this.application.countryId) {
                    this.selectedCountryId = this.application.countryId;
                }
                this.singleSelectProcedures = [...this.countryLabelProcedures, ...this.memberStateLabelProcedures];
                this.multiSelectProcedures = [...this.countriesLabelProcedures, ...this.memberStateLabelProcedures];
            },
            watch: {
                concernedMemberStatesRequired(newValue) {
                    // Sync the Vue.js toggle state with the hidden form field
                    const hiddenField = document.getElementById('hiddenConcernedMemberStatesRequired');
                    if (hiddenField) {
                        hiddenField.value = newValue;
                    }
                }
            }
        };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/FilteredTable" />
    <partial name="Components/YesNoDialog" />
}